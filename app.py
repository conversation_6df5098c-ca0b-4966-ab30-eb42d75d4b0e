# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/9 4:47 下午
@Auth ： Jinx
@File ：home.py.py
@IDE ：PyCharm
"""
import logging
import os
import sys

import streamlit as st

from pages import init_streamlit
from pages.components.sidebar import render_chat_sidebar
from pages.components.history import show_conversation_history, clear_quotes
from pages.components.chat import run

# 设置页面为宽页模式
st.set_page_config(layout="wide")

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
    ]
)

init_streamlit()

# 初始化MCP相关环境变量
if "mcp_enabled" not in st.session_state:
    st.session_state.mcp_enabled = os.environ.get("MCP_ENABLED", "false").lower() == "true"
    os.environ["MCP_ENABLED"] = str(st.session_state.mcp_enabled).lower()

if "mcp_config" not in st.session_state:
    st.session_state.mcp_config = {}
    os.environ["MCP_CONFIG"] = "{}"

# 页面标题
st.title("April Assistant Chat")
# st.subheader("Requirement, Research, Plan, Execute")
st.markdown("---")

# 侧边栏：记忆管理
with st.sidebar:
    render_chat_sidebar()

show_conversation_history()

if "following_group_id" in st.session_state and st.session_state.following_group_id:
    st.warning(f"You are following a group conversation: {st.session_state.following_group_id}. ")

# 处理用户输入
if prompt := st.chat_input("Type something..."):
    history = st.session_state.quoted_history.copy()
    response = run(prompt, history)
    clear_quotes()
