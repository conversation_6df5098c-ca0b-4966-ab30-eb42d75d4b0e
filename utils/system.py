"""
    system.py
"""
import platform
from datetime import datetime

def get_system_info_and_time() -> str:
    """get_system_info_and_time"""
    now = datetime.now()
    info = (
        f"系统名称: {platform.system()}\n"
        f"系统版本: {platform.version()}\n"
        f"机器类型: {platform.machine()}\n"
        f"处理器信息: {platform.processor()}\n"
        f"平台信息: {platform.platform()}\n"
        f"节点名称（主机名）: {platform.node()}\n"
        f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}"
    )
    return info

# 调用示例
if __name__ == "__main__":
    print(get_system_info_and_time())