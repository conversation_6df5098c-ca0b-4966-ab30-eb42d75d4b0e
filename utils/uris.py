"""
    uris.py

    uri 的相关操作
"""

from urllib.parse import urlparse, parse_qs
from typing import List, <PERSON><PERSON>

def analyze_uris_subdirs_files(mems, uri_prefix) -> Tuple[List[str], List[str]]:
    """
        分析 uri，找出在 uri_prefix 下的子目录和文件， uri 类型是：
            /path/to/dir/ 或者 /path/to/file.md
    参数：
        uri_prefix 必须是完整的一个路径，比如 /path/to/dir/ 或者 /path/to/file.md，
        mems 是一个列表，每个元素是一个 Memory 对象，包含 uri 属性
    返回：
        一个元组，第一个元素是子目录列表，第二个元素是文件列表
    """
    # 解析 uri_prefix
    parsed_prefix = urlparse(uri_prefix)
    prefix_path = parsed_prefix.path

    subdirectories = set()
    files = set()

    for mem in mems:
        # 解析每个 mem 的 uri
        parsed_uri = urlparse(mem.uri)
        path = parsed_uri.path

        # 检查是否在 prefix 下
        if path.startswith(prefix_path):
            relative_path = path[len(prefix_path):].strip('/')
            parts = relative_path.split('/')

            if len(parts) > 1:
                # 如果有多个部分，第一个部分是子目录
                subdirectories.add(parts[0])
            elif len(parts) == 1 and parts[0]:
                # 如果只有一个部分，且不为空，则是文件
                files.add(parts[0])

    return list(subdirectories), list(files)

if __name__ == "__main__":
    # 使用示例
    class Mem:
        """Mem"""
        def __init__(self, uri):
            self.uri = uri

    mems = [
        Mem("/docs/folder1/file1.md?label=example&id=1"),
        Mem("/docs/folder1/file2.md?label=test&id=2"),
        Mem("/docs/folder2/file3.md?label=sample&id=3"),
        Mem("/docs/file4.md?label=root&id=4"),
    ]

    uri_prefix = "/docs"
    subdirs, files = analyze_uris_subdirs_files(mems, uri_prefix)

    print("Subdirectories:", subdirs)
    print("Files:", files)