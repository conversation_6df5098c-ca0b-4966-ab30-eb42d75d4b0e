#!/usr/bin/env python3
"""
MCP命令行工具
"""
import sys
import json
import argparse
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append('/app')

from functions.universal_mcp import universal_mcp

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MCP命令行工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # 列出服务命令
    list_parser = subparsers.add_parser('list', help='列出所有MCP服务')

    # 列出工具命令
    tools_parser = subparsers.add_parser('tools', help='列出服务的工具')
    tools_parser.add_argument('server', help='服务名称')

    # 调用工具命令
    call_parser = subparsers.add_parser('call', help='调用MCP工具')
    call_parser.add_argument('server', help='服务名称')
    call_parser.add_argument('tool', help='工具名称')
    call_parser.add_argument('--args', help='工具参数(JSON格式)', default='{}')

    # 快速查询命令
    query_parser = subparsers.add_parser('query', help='快速查询火车票')
    query_parser.add_argument('--from', dest='from_city', required=True, help='出发城市')
    query_parser.add_argument('--to', dest='to_city', required=True, help='到达城市')
    query_parser.add_argument('--date', default='tomorrow', help='日期(默认明天)')

    args = parser.parse_args()

    if args.command == 'list':
        list_services()
    elif args.command == 'tools':
        list_tools(args.server)
    elif args.command == 'call':
        call_tool(args.server, args.tool, args.args)
    elif args.command == 'query':
        query_trains(args.from_city, args.to_city, args.date)
    else:
        parser.print_help()

def list_services():
    """列出所有MCP服务"""
    print("=== MCP服务列表 ===")

    # 获取所有服务配置
    all_servers = universal_mcp.manager.get_all_servers()

    if not all_servers:
        print("未找到任何MCP服务配置")
        return

    for name, config in all_servers.items():
        enabled = config.get('enabled', False)
        connection_type = config.get('connection_type', 'stdio')
        status = universal_mcp.manager.get_server_status(name)

        status_icon = "🟢" if status == "running" else "🟡" if status == "ready" else "🔴" if status == "stopped" else "⚪"
        enabled_text = "启用" if enabled else "禁用"

        print(f"{status_icon} {name}")
        print(f"   状态: {status} ({enabled_text})")
        print(f"   类型: {connection_type}")

        if connection_type == "http":
            server_name = config.get('server_name', '')
            print(f"   服务: {server_name}")
        else:
            command = config.get('command', '')
            args = config.get('args', [])
            print(f"   命令: {command} {' '.join(args)}")
        print()

def list_tools(server_name: str):
    """列出服务的工具"""
    print(f"=== {server_name} 服务工具列表 ===")

    result = universal_mcp.get_available_tools(server_name)

    if not result["success"]:
        print(f"❌ 获取工具列表失败: {result['error']}")
        return

    tools = result.get("tools", [])

    if not tools:
        print("该服务没有可用工具")
        return

    for i, tool in enumerate(tools, 1):
        print(f"{i}. {tool['name']}")
        if tool.get('description'):
            print(f"   描述: {tool['description']}")

        if tool.get('inputSchema'):
            schema = tool['inputSchema']
            if 'properties' in schema:
                print("   参数:")
                for param_name, param_info in schema['properties'].items():
                    param_type = param_info.get('type', 'unknown')
                    param_desc = param_info.get('description', '')
                    required = param_name in schema.get('required', [])
                    required_text = " (必需)" if required else " (可选)"
                    print(f"     - {param_name}: {param_type}{required_text}")
                    if param_desc:
                        print(f"       {param_desc}")
        print()

def call_tool(server_name: str, tool_name: str, args_json: str):
    """调用MCP工具"""
    print(f"=== 调用 {server_name}.{tool_name} ===")

    try:
        # 解析参数
        arguments = json.loads(args_json)
    except json.JSONDecodeError as e:
        print(f"❌ 参数JSON格式错误: {e}")
        return

    print(f"参数: {json.dumps(arguments, ensure_ascii=False, indent=2)}")
    print("调用中...")

    # 调用工具
    success, result = universal_mcp.use_mcp_tool(server_name, tool_name, arguments)

    if success:
        print("✅ 调用成功")
        print("结果:")
        print(result)
    else:
        print(f"❌ 调用失败: {result}")

def query_trains(from_city: str, to_city: str, date: str):
    """快速查询火车票"""
    print(f"=== 查询火车票: {from_city} → {to_city} ({date}) ===")

    # 构建参数
    arguments = {
        "fromCity": from_city,
        "toCity": to_city,
        "date": date
    }

    print(f"查询参数: {json.dumps(arguments, ensure_ascii=False)}")
    print("查询中...")

    # 调用chinarailway服务的search工具
    success, result = universal_mcp.use_mcp_tool("chinarailway", "search", arguments)

    if success:
        print("✅ 查询成功")
        print("查询结果:")
        print(result)
    else:
        print(f"❌ 查询失败: {result}")

if __name__ == "__main__":
    main()
