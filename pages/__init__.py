"""
    __init__.py
"""
from dotenv import load_dotenv
import streamlit as st
from core.memory import MemoryManager
from core.session import SessionManager  # 更新导入路径

# 加载环境变量
load_dotenv()

def init_streamlit():
    """初始化 Streamlit 会话状态"""
    if 'memory_manager' not in st.session_state:
        st.session_state.memory_manager = MemoryManager()

    if 'session_manager' not in st.session_state:
        st.session_state.session_manager = SessionManager()

    if 'quoted_history' not in st.session_state:
        st.session_state.quoted_history = []

    if 'session' not in st.session_state:
        sessions = st.session_state.session_manager.list()
        if len(sessions) == 0:
            st.session_state.session = st.session_state.session_manager.new({"session_name": "New Session"})
        else:
            session_info = sessions[0]
            st.session_state.session = st.session_state.session_manager.get(session_info)

    if 'quoted_ids' not in st.session_state:
        st.session_state.quoted_ids = set()  # 用于跟踪已引用的对话ID

    if "quotes" not in st.session_state:
        st.session_state.quotes = {}

    # 初始化MCP相关状态
    if "mcp_enabled" not in st.session_state:
        import os
        st.session_state.mcp_enabled = os.environ.get("MCP_ENABLED", "false").lower() == "true"
        os.environ["MCP_ENABLED"] = str(st.session_state.mcp_enabled).lower()

    if "mcp_config" not in st.session_state:
        import os
        import json
        st.session_state.mcp_config = {}
        os.environ["MCP_CONFIG"] = "{}"

    if "interactive_counter" not in st.session_state:
        st.session_state.interactive_counter = 0