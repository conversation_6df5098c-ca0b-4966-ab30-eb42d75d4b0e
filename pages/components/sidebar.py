"""
    __init__.py
"""
import os
import json
import logging
import streamlit as st
from openai import OpenAI


@st.dialog("New Session")
def create_session_dialog():
    """"""
    session_name = st.text_input("请输入session的名字:", value="New Session", max_chars=100)
    already_existed_session_names = [s["session_name"] for s in st.session_state.session_manager.list()]
    if session_name in already_existed_session_names:
        st.error("Session already existed")
    if st.button("确认"):
        st.session_state.session = st.session_state.session_manager.new({"session_name": session_name})
        st.rerun()


@st.dialog("Select Session")
def select_session_dialog():
    """"""
    session_name = st.selectbox("Select Session", 
        [f"{s['session_name']}: {s['session_id'][:5]}" for s in st.session_state.session_manager.list()])
    if st.button("Select"):
        st.session_state.session = st.session_state.session_manager.get({"session_name": session_name.split(":")[0]})
        st.rerun()


@st.dialog("MCP Configuration")
def mcp_config_dialog():
    """MCP配置对话框"""
    # 获取当前MCP配置
    current_mcp_config = json.loads(os.environ.get("MCP_CONFIG", "{}"))

    # 显示当前配置
    st.json(current_mcp_config)

    # 编辑配置
    mcp_config_str = st.text_area(
        "MCP配置 (JSON格式)",
        value=json.dumps(current_mcp_config, indent=2, ensure_ascii=False),
        height=300
    )

    if st.button("保存MCP配置"):
        try:
            # 验证JSON格式
            new_config = json.loads(mcp_config_str)

            # 保存到环境变量和session_state
            os.environ["MCP_CONFIG"] = json.dumps(new_config)
            st.session_state.mcp_config = new_config

            st.success("MCP配置已保存")
            st.rerun()
        except json.JSONDecodeError as e:
            st.error(f"JSON格式错误: {str(e)}")


def render_chat_sidebar():
    """"""
    st.header("Session Management")

    select_session_col, new_session_col = st.columns(2)
    # 选择session的按钮
    with select_session_col:
        if st.button("Select Session"):
            select_session_dialog()

    # 新建session按钮
    with new_session_col:
        if st.button("New Session"):
            create_session_dialog()

    # 使用 selectbox 创建选择prompt下拉框(模型人设)
    character_setting_selected_option = [f.split(".")[0] for f in os.listdir("./prompts")]
    character_setting = st.selectbox("Select Character Setting", character_setting_selected_option)
    st.session_state.prompt_file_path = f"./prompts/{character_setting}.md"
    
    st.markdown("---")
    # MCP服务配置部分
    st.header("MCP Configuration")

    # MCP服务开关
    mcp_enabled = st.toggle(
        "启用MCP服务",
        value=os.environ.get("MCP_ENABLED", "false").lower() == "true",
        help="开启或关闭MCP服务"
    )

    # 更新MCP服务状态
    if mcp_enabled != (os.environ.get("MCP_ENABLED", "false").lower() == "true"):
        os.environ["MCP_ENABLED"] = str(mcp_enabled).lower()
        st.session_state.mcp_enabled = mcp_enabled

    # MCP配置按钮
    if st.button("修改MCP配置"):
        mcp_config_dialog()

    # 显示当前MCP配置状态
    if mcp_enabled:
        st.success("MCP服务已启用")

        # 显示当前配置的服务器
        current_mcp_config = json.loads(os.environ.get("MCP_CONFIG", "{}"))
        if current_mcp_config:
            st.write("已配置的MCP服务器:")
            for server_name in current_mcp_config.keys():
                st.write(f"- {server_name}")
        else:
            st.warning("未配置MCP服务器，请点击'修改MCP配置'进行设置")
    else:
        st.warning("MCP服务已禁用")

    st.markdown("---")
    # API密钥设置部分
    st.header("API Configuration")

    # 获取当前配置（优先session_state，其次环境变量）
    current_config = {
        "base_url": st.session_state.get("chat_base_url", os.getenv("BASE_URL", "")),
        "api_key": st.session_state.get("api_key", os.getenv("API_KEY", "")),
        "model": st.session_state.get("selected_model", os.getenv("MODEL", ""))
    }

    # 默认显示当前参数（只要有任一配置存在就显示）
    if any(current_config.values()):
        st.markdown(f"- Base URL: {current_config['base_url'] or 'Not set'}\n"
                    f"- Model: {current_config['model'] or 'Not set'}\n")

    # 点击按钮显示/隐藏修改表单
    if st.button("Modify API Configuration"):
        st.session_state.show_api_config = not st.session_state.get("show_api_config", False)

    # 修改配置表单
    if st.session_state.get("show_api_config", False):
        with st.form("api_config_form"):
            new_base_url = st.text_input(
                "API Base URL",
                value=current_config["base_url"],
                help="Enter your API base URL (e.g., https://api.example.com)"
            )

            new_api_key = st.text_input(
                "API Key",
                value=current_config["api_key"],
                type="password",
                help="Enter your API key"
            )

            # 表单提交按钮
            submitted = st.form_submit_button("Save Configuration")
            if submitted:
                if new_base_url and new_api_key:
                    # 保存新配置
                    st.session_state.chat_base_url = new_base_url
                    st.session_state.api_key = new_api_key
                    os.environ["BASE_URL"] = new_base_url
                    os.environ["API_KEY"] = new_api_key

                    # 尝试获取模型列表
                    try:
                        chat_client = OpenAI(
                            base_url=new_base_url,
                            api_key=new_api_key
                        )
                        model_list = [model.id for model in chat_client.models.list().data]

                        if model_list:
                            st.session_state.model_list = model_list
                            st.session_state.selected_model = model_list[0]
                            os.environ["MODEL"] = model_list[0]
                            current_config["model"] = model_list[0]

                        st.success("Configuration saved successfully!")
                        st.session_state.show_api_config = False
                        st.rerun()
                    except Exception as e:
                        st.error(f"Failed to initialize client: {str(e)}")
                else:
                    st.error("Please provide both Base URL and API Key")

    # 模型选择（仅在配置有效且有模型列表时显示）
    if "model_list" in st.session_state and st.session_state.model_list:
        current_model = current_config["model"]
        model_index = st.session_state.model_list.index(
            current_model) if current_model in st.session_state.model_list else 0

        selected_model = st.selectbox(
            "Select Model",
            options=st.session_state.model_list,
            index=model_index
        )

        if selected_model != current_model:
            st.session_state.selected_model = selected_model
            os.environ["MODEL"] = selected_model
            st.rerun()

    # 测试连接按钮（仅在配置完整时显示）
    if all(current_config.values()):
        if st.button("Test Connection"):
            try:
                client = OpenAI(
                    base_url=current_config["base_url"],
                    api_key=current_config["api_key"]
                )
                models = client.models.list()
                st.success("Connection successful!")

                # 显示可用模型（最多5个）
                st.write("Available models:")
                for model in models.data[:5]:
                    st.write(f"- {model.id}")
            except Exception as e:
                st.error(f"Connection failed: {str(e)}")
