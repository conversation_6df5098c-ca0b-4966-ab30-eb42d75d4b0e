"""
    memory.py
"""
import os
import re
import time
import json

import streamlit as st
from typing import Optional, List, Tuple
from core.memory import Label, LabelManager, MemoryManager
from functions.memory import generate_summary_and_labels, _analyze_query

@st.dialog("Change Memory Path")
def change_memory_path_dialog():
    """"""
    selected_path = st.text_input("请输入文件夹路径:", value=st.session_state.home_dir)

    if st.button("确认"):
        if selected_path and os.path.exists(selected_path):
            st.session_state.home_dir = selected_path
            st.session_state.memory_manager.load_memory(st.session_state.home_dir)
            st.session_state.memories = st.session_state.memory_manager._memory_data[::-1][:10]
            st.success("路径已成功更改！")
            
            st.rerun()
        else:
            st.error("无效的文件夹路径")


@st.dialog("Are you sure you want to save all modifications？")
def save_all_change_dialog():
    """"""
    if st.button("确认"):
        st.session_state.memory_manager.save_memory()
        st.rerun()


@st.dialog("Add your Memory")
def add_memory_dialog(memory_text: Optional[str] = None, memory_summary: Optional[str] = None,
        labels_str: Optional[str] = None, store_path: Optional[str] = None):
    """
        添加记忆的对话框
    """
    if "memory_text" not in st.session_state:
        if memory_text is None:
            st.session_state.memory_text = "Please input your memory text here."
        else:
            st.session_state.memory_text = memory_text
    memory_text = st.text_area("Memory Text", key="memory_text")

    if "memory_summary" not in st.session_state:
        if memory_summary is None:
            st.session_state.memory_summary = "Please input your memory summary here."
        else:
            st.session_state.memory_summary = memory_summary
    memory_summary = st.text_area("Summary", key="memory_summary")
    
    # 获取所有已知标签选项
    label_options = [label['name'] for label in st.session_state.memory_manager.list_labels()]
    
    if "lm_suggest_labels" not in st.session_state:
        if labels_str is None:
            st.session_state.lm_suggest_labels = []
            st.session_state.memory_labels = []
            st.session_state.new_labels = ""
        else:
            _tp_lm = LabelManager.from_markdown(labels_str)
            st.session_state.lm_suggest_labels = _tp_lm.labels.values()

            # 取LM建议同时在列表里的标签
            st.session_state.memory_labels = [lab.label for lab in st.session_state.lm_suggest_labels \
                if lab.label in label_options]
            st.session_state.new_labels = "\n".join([lab.to_markdown() for lab in st.session_state.lm_suggest_labels \
                if lab.label not in label_options])
    
    # 使用multiselect显示标签，并绑定到selected_labels
    selected_labels = st.multiselect("Select Labels", options=label_options, key="memory_labels")
    new_labels = st.text_area("New Labels:", key="new_labels")

    if "save_path" not in st.session_state:
        if store_path is None:
            st.session_state.save_path = os.path.join(st.session_state.home_dir, "")
        else:
            st.session_state.save_path = store_path
    save_path = st.text_input("save_path:", key="save_path")
    
    # 创建两列布局，一列放提交按钮，一列放建议按钮
    col1, col2 = st.columns(2)

    def on_suggest():
        if memory_text:
            st.session_state.memory_summary, label_str, store_path = generate_summary_and_labels(memory_text,
                label_text=st.session_state.memory_manager.labels_description,
                path_description=st.session_state.memory_manager.path_description
                )
            
            lm_suggest_labels = LabelManager.from_markdown(label_str).labels.values()
            st.session_state.new_labels = "\n".join([lab.to_markdown() for lab in lm_suggest_labels \
                if lab.label not in label_options])
            st.session_state.memory_labels = [lab.label for lab in lm_suggest_labels if lab.label in label_options]
            st.session_state.save_path = store_path if store_path.startswith(st.session_state.home_dir) \
                else os.path.join(st.session_state.home_dir, store_path)
            st.session_state.lm_suggest_labels = lm_suggest_labels
            st.success("已生成建议标签和路径！")
    
    with col1:
        submit_button = st.button(label='Submit Memory', key="submit_memory")
        if submit_button:
            if new_labels:
                # 如果有新的标签，添加到标签管理器中
                for lab in st.session_state.lm_suggest_labels:
                    if lab.label not in label_options:
                        st.session_state.memory_manager.add_label(lab.label, lab.description, lab.category)
                        selected_labels.append(lab.label)
                    
                    # else:
                    # TBD 对于已经存在的标签，是否要修改呢？
            
            st.session_state.memory_manager.add_memory(
                memory_text=memory_text,
                summary=memory_summary,
                labels=selected_labels,
                save_path=save_path
            )
            
            st.success("Memory added successfully!")
            del st.session_state["memory_text"]
            del st.session_state["memory_summary"]
            del st.session_state["memory_labels"]
            del st.session_state["new_labels"]
            del st.session_state["save_path"]
            del st.session_state["lm_suggest_labels"]
            st.rerun()
    
    with col2:
        suggest_button = st.button(label='Suggest', key="suggest_button", on_click=on_suggest)


@st.dialog("Update Memory")
def update_memory_dialog(memory_id, memory_manager):
    """"""
    mem = None
    for m in memory_manager._memory_data:
        if m.id == memory_id:
            mem = m
            break
            
    if mem is None:
        st.error(f"Memory {memory_id} not found")
        return
 
    new_text = st.text_area("New Text", value=mem.original_text, key="new_text")
    new_summary = st.text_area("New Summary", value=mem.summary, key="new_summary")
    new_metadata = st.text_area("New Metadata", value=json.dumps(mem.metadata), key="new_metadata", \
         help="Enter metadata as JSON")

    # 标签选择
    label_options = [label['name'] for label in st.session_state.memory_manager.list_labels()]
    new_labels = st.multiselect("New Labels", options=label_options, default=mem.labels, key="new_labels")
    
    # 显示当前路径（放在文件上传器之后，确保用户可以看到当前选择的路径）
    if "path_display" not in st.session_state:
        print("mem.uri:", mem.uri)
        st.session_state.path_display = mem.uri.split("?")[0]
    save_path = st.text_input("当前记忆存储根路径", key="path_display")
    
    if st.button("Update Memory", key="update_memory"):
        memory_manager.update_memory(
            memory_id=mem.id,
            new_text=new_text,
            new_summary=new_summary,
            new_labels=new_labels,
            new_metadata=new_metadata,
            save_path=save_path
        )
        st.success(f"Memory {mem.id} updated")
        st.rerun()


def display_memories(memories, memory_manager):
    """"""
    if not memories:
        st.write("No memories found")
        return
    
    # 创建标签ID到名称的映射
    # label_map = {label['id']: label['name'] for label in memory_manager.list_labels()}
    for mem in memories:
        if not mem:
            continue
        col1, col2, col3 = st.columns([6, 1, 1])
        with col1:
            with st.container():
                col4, col5 = st.columns([1, 1])
                with col4:
                    st.write("Created")
                    st.write(mem.created_at)
                with col5:
                    st.write("Updated")
                    st.write(mem.updated_at)
 
            st.markdown(mem.summary)
            with st.expander("Show Original"):
                st.markdown(mem.original_text)
            st.markdown(f"URI: `{mem.uri}`")
                
        with col2:
            st.write("Labels")
            for label in mem.labels:
                st.write(f"`{label}`")
                
        with col3:
            st.write("Actions")
            if st.button(f"Delete {mem.id}", key=f"delete_memory_{mem.id}", icon="🗑️"):
                memory_manager.delete_memory(mem.id)
                st.success(f"Memory {mem.id} deleted")
                st.rerun()
            if st.button(f"Update {mem.id}", key=f"update_memory_{mem.id}", icon="✏️"):
                update_memory_dialog(mem.id, memory_manager)
 
        st.markdown("---")


def remember(content: str, requires_approval: str = "false") -> Tuple[bool, str]:
    """
        记录记忆
        通常发生在
        1. 用户明确要求记住某些信息时，
        2. 明显能提取出用户的特征（如用户的个体特征，环境特征，习惯特征等）
    """
    if not content:
        return False, "No content to remember"
    
    # 处理内容
    memory_manager = st.session_state.get('memory_manager')
    if not memory_manager:
        return False, "Memory manager not found in session state"
    
    # 生成摘要和标签
    summary, label_str, save_path = generate_summary_and_labels(content, memory_manager.labels_description, \
        memory_manager.path_description)
    
    # 添加记忆
    try:
        # add_memory_dialog(memory_text=content, memory_summary=summary, labels_str=label_str, store_path=save_path)
        _tp_lm = LabelManager.from_markdown(label_str)
        for lab in _tp_lm.labels.values():
            memory_manager.add_label(lab.label, lab.description, lab.category)
        memory_manager.add_memory(content, summary, labels=[lab.label for lab in _tp_lm.labels.values()], \
            save_path=save_path)
    except Exception as e:
        return False, f"Error adding memory: {str(e)}"

    return True, f"Memory added success. \n- summary: {summary}\n- save_path: {save_path}\n- labels:\n{label_str}"


def think_back(query: str) -> Tuple[bool, str]:
    """
    基于自然语言query检索记忆

    参数:
        query: 自然语言查询(如"上周关于AI的讨论"、"与项目X相关的文档")

    返回:
        Tuple[bool, str]: (成功状态, JSON格式的记忆结果或错误信息)
    """
    memory_manager = st.session_state.get('memory_manager')
    if not memory_manager:
        return False, "Memory manager not found in session state"

    try:
        # 1. 使用LLM分析query并生成搜索参数
        search_params = _analyze_query(query, memory_manager)
        print(f"Search parameters: {search_params}")
        # 2. 执行记忆搜索
        memories = []
        for param in search_params:
            results = memory_manager.search_memory(
                query=param["query"],
                k=param.get("limit", 10),
                search_type=param["type"]
            )
            memories.extend(results)

        # 3. 去重并排序(按相关性或时间)
        unique_memories = _deduplicate_and_sort(memories)

        # 4. 格式化结果
        result = "===\n\n# 相关记忆\n" + "\n".join([
            f"- **Created At**: {mem.created_at}\n"
            f"- **Updated At**: {mem.updated_at}\n"
            f"- **Content**: `{mem.original_text}`\n"
            for mem in unique_memories]
        )
        return True, json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        return False, f"Error in think_back: {str(e)}"

def _deduplicate_and_sort(memories: List) -> List:
    """记忆去重和排序"""
    seen = set()
    unique_memories = []
    for mem in memories:
        if mem.id not in seen:
            seen.add(mem.id)
            unique_memories.append(mem)
    # 按更新时间降序排序
    return sorted(unique_memories, key=lambda x: x.updated_at, reverse=True)