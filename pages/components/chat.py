import os
import re
import json
import sys
import logging
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from uuid import UUID, uuid4

import streamlit as st
from openai import OpenAI

from utils.system import get_system_info_and_time
from core.memory import Memory, MemoryManager
from functions.filesystem import *
from functions.mysql_interperter import execute_sql
from pages.components.memory import remember

abilities = {
    "execute_command": execute_command,
    "write_to_file": write_to_file,
    "read_file": read_file,
    "read_file_structure": read_file_structure,
    "list_files_in_directory": list_files_in_directory,
    "replace_in_file": replace_in_file,
    "search_files": search_files,
    "list_files": list_files,
    "list_code_definition_names": list_code_definition_names,
    "completion_task": completion_task,
    "remember": remember,
    "execute_sql": execute_sql,
}

STREAM_MODE = True

def get_relevant_memories(query: str, extract_memory_tag: bool = True) -> List[Memory]:
    """获取与查询相关的记忆

    Args:
        query: 查询内容

    Returns:
        相关的记忆列表
    """
    if extract_memory_tag:
        mem = re.search(r"<memory>(.*?)</memory>", query, flags=re.DOTALL)
        if mem:
            query = mem.group(1)
        else:
            return []

    memory_manager = st.session_state.get('memory_manager')

    search_labels = []
    for label in memory_manager.labels.values():
        if label.label in query:
            search_labels.append(label)
            continue

        if not label.synonyms:
            continue

        for synonym in label.synonyms.split(","):
            if synonym in query:
                search_labels.append(label)
                break

    logging.info("search_labels:" + str([label.label for label in search_labels]))
    relevant_memories = memory_manager.search_memory(query, [i.id for i in search_labels], k=5, search_type="label")
    logging.info(f"for query: {query}, relevant_memories: {relevant_memories}")
    return relevant_memories


def build_system_prompt_with_memory(query: str, plan: str) -> str:
    """构建系统提示"""
    # fetch long-term memories by query
    relevant_memories = get_relevant_memories(query)

    # build relevant memories context
    memory_context = "\n# 和用户有关的记忆：\n"
    for memory in relevant_memories:
        memory_context += f"\ncreated at [{memory.created_at}]\nsummary:\n{memory.original_text}\n"

    if plan:
        memory_context += f"\n\n当前正在执行的计划：\n{plan}\n"

    prompt = memory_context
    return prompt


def xml_exact(llm_output: str) -> Tuple[str, str, Dict[str, str]]:
    """
    解析 LLM 的输出，提取工具调用信息

    Args:
        llm_output (str): LLM 生成的文本

    Returns:
        tuple: 是否成功, 工具名, 参数字典
    """
    # 检查是否是MCP工具调用
    mcp_tool_match = re.search(
        r'<use_mcp_tool>\s*(.*?)\s*</use_mcp_tool>',
        llm_output,
        re.DOTALL
    )

    # 添加调试日志
    # print(f"检查MCP工具调用: {'匹配成功' if mcp_tool_match else '未匹配'}")
    # if mcp_tool_match:
    #     print(f"MCP工具调用内容: {mcp_tool_match.group(0)}")

    if mcp_tool_match:
        # 解析MCP工具调用
        mcp_content = mcp_tool_match.group(1).strip()

        # 提取服务器名称
        server_name_match = re.search(r'<server_name>\s*(.*?)\s*</server_name>', mcp_content, re.DOTALL)
        server_name = server_name_match.group(1).strip() if server_name_match else ""

        # 提取工具名称
        tool_name_match = re.search(r'<tool_name>\s*(.*?)\s*</tool_name>', mcp_content, re.DOTALL)
        tool_name = tool_name_match.group(1).strip() if tool_name_match else ""

        # 提取参数
        arguments_match = re.search(r'<arguments>\s*(.*?)\s*</arguments>', mcp_content, re.DOTALL)
        arguments = arguments_match.group(1).strip() if arguments_match else "{}"

        # 检查必要参数是否存在
        if not server_name or not tool_name:
            return "MCP工具调用缺少必要参数", "", {}

        return "参数解析成功", "use_mcp_tool", {
            "server_name": server_name,
            "tool_name": tool_name,
            "arguments": arguments
        }

    # 匹配最外层工具调用标签（支持跨行匹配）
    tool_tag_match = re.findall(
        r'<(\w+)>(.*?)</\1>',  # 使用反向引用确保标签闭合
        llm_output,
        re.DOTALL  # 允许 . 匹配换行符
    )

    if not tool_tag_match or (len(tool_tag_match) == 1 and tool_tag_match[0][0] == "thinking"):
        return "未检测到正确的工具调用", "completion_task", {"result": "未检测到正确的工具调用"}  # 未检测到工具调用

    tool_name, inner_content = tool_tag_match[-1]
    inner_content = inner_content.strip()  # 去除首尾空白

    # 解析参数标签（支持嵌套参数值中的XML字符，需LLM自行转义）
    param_pattern = re.compile(
        r'<(\w+)>(.*?)</\1>',  # 使用非贪婪匹配
        re.DOTALL
    )

    # 提取所有参数并存入字典
    params = {}
    for param_name, param_value in param_pattern.findall(inner_content):
        params[param_name] = param_value.strip()  # 去除参数值首尾空白

    # 检查内容合法性：所有内容都应该是参数标签
    sanitized_content = param_pattern.sub('', inner_content)  # 移除所有参数标签
    if sanitized_content.strip():  # 存在非参数内容
        print(f"sanitized_content: {sanitized_content}")
        return "存在非参数内容", "", {}

    return "参数解析成功", tool_name, params


def execute_action(action: str, group_id: str) -> Tuple[bool, str]:
    """
    Execute action

    Args:
        action (str): Action in workflow
        group_id (str): Group ID

    Returns:
        dict: Function call
    """
    is_end = False
    try:
        # 添加调试日志
        print(f"执行动作: {action[:100]}...")  # 只打印前100个字符，避免日志过长

        message, function, params = xml_exact(action)

        # 添加调试日志
        print(f"解析结果: message={message}, function={function}, params={params}")
    except Exception as e:
        logging.error(f"Error parsing action: {e}")
        return True, f"ERROR PARSING TOOL: {e}"

    if not function:
        return True, f"ERROR PARSING TOOL: {message}"

    if function in ["ask_user_check", "completion_task"]:
        if function == "ask_user_check":
            st.session_state.following_group_id = group_id
        is_end = True

    try:
        is_success, result = abilities.get(function)(**params)
        result = f"FUNCTION CALL: {function}\nPARAMS: {params}\nSUCCESS: {is_success}\nRESULT: {result}"
    except Exception as e:
        logging.error(f"Error executing {function}: {e}")
        result = f"ERROR EXECUTING FUNCTION: {function}\nPARAMS: {params}, \nERROR: {e}"

    return is_end, result


def think(content: str, history: List[Dict] = None) -> Tuple[str, Dict]:
    """
        think
    """
    # system_prompt = build_system_prompt_with_memory(content, environment.get("plan", ""))
    _, system_prompt = read_file(st.session_state.prompt_file_path)
    with st.chat_message("user"):
        with st.expander("System Prompt"):
            st.write(system_prompt)
        st.write(content)

    token_count = {}
    messages = [{"role": "system", "content": system_prompt}] + \
               [{"role": m["role"], "content": m["content"]} for m in history]

    with st.chat_message("assistant"):
        try:
            chat_client = OpenAI(base_url=os.getenv("BASE_URL"), api_key=os.getenv("API_KEY"))
            if STREAM_MODE:
                stream = chat_client.chat.completions.create(
                    model=os.getenv("MODEL"),
                    messages=messages,
                    stream=True,
                )
                response = st.write_stream(stream)
            else:
                nostream = chat_client.chat.completions.create(
                    model=os.getenv("MODEL"),
                    messages=messages,
                )
                response = nostream.choices[0].message.content
                usage = nostream.usage
                st.markdown(response)
                token_count = {"total_tokens": usage.total_tokens, "prompt_tokens": usage.prompt_tokens, 
                    "completion_tokens": usage.completion_tokens}

        except Exception as e:
            st.error(f"An error occurred: {str(e)}")
            logging.error(f"Chat error: {str(e)}", exc_info=True)
            return f"An error occurred: {str(e)}"

    return response, token_count


def run(query: str, history: List[Dict] = None) -> str:
    """执行
    """
    user_response = query
    epoch = 1
    max_epoch = 10
    group_id = str(uuid4())
    token_count = {}

    def _build_message(role: str, content: str) -> Dict:
        return {
            "role": role,
            "time": str(datetime.now()),
            "content": content,
            "id": f"{role}_{str(uuid4())}",
            "group_id": group_id,
            "token_count": token_count
        }
    
    if "following_group_id" in st.session_state and st.session_state.following_group_id:
        group_id = st.session_state.following_group_id
        del st.session_state["following_group_id"]
        for m in st.session_state.session.conversation:
            if "group_id" in m and m["group_id"] == group_id:
                history.append(m)

    while epoch < max_epoch:
        # 记录对话历史
        llm_input = _build_message("user", user_response)
        st.session_state.session.conversation.append(llm_input)  # 记录到页面
        history.append(llm_input)  # 记录到下次对话

        # thinking
        response, token_count = think(user_response, history)

        llm_output = _build_message("assistant", response)
        llm_output["model"] = {"name": os.getenv("MODEL")}
        
        st.session_state.session.conversation.append(llm_output)
        history.append(llm_output)
        st.session_state.session_manager.save(st.session_state.session)

        # action
        is_end, execute_action_result = execute_action(response, group_id)

        # Show as user called the function
        user_response = execute_action_result
        # with st.chat_message("user"):
        #     st.markdown(user_response)

        if is_end:
            break

        epoch += 1
    
    if epoch == max_epoch:
        user_response = "最大迭代次数已达到，结束对话。\n最近一次 Reason 结果为：\n" + response + \
                        "最近一次 Action 结果为：\n" + user_response

    return user_response


def ask_user_check(question: str) -> Tuple[bool, str]:
    """
    Ask the user a followup question using Streamlit radio buttons.
    Blocks until user responds.

    Args:
        question: The question to ask the user

    Returns:
        Tuple containing (success_status, response)
        - success: True if user provided valid input
        - response: set result
    """
    
    return True, "agree"
    

abilities["ask_user_check"] = ask_user_check
