# 使用Python官方镜像
FROM python:3.12

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive
ENV PIP_DISABLE_PIP_VERSION_CHECK=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_PROGRESS_BAR=off
ENV PIP_QUIET=1
ENV PIP_NO_BUILD_ISOLATION=1




#
# 设置工作目录
WORKDIR /app



# # 安装Node.js 20.x (用于MCP工具)
# RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
#     apt-get install -y nodejs && \
#     apt-get clean && \
#     rm -rf /var/lib/apt/lists/* && \
#     npm cache clean --force

# 设置pip镜像源
ARG PIP_INDEX_URL="https://pip.baidu-int.com/simple/"
ENV PIP_INDEX_URL=${PIP_INDEX_URL}
ENV STREAMLIT_WATCHDOG=false


COPY requirements.txt .
RUN  pip install --no-cache-dir --retries 5 --timeout 60 -r requirements.txt

# 升级pip并安装Python依赖

# 复制应用代码
COPY . .

# 设置入口点脚本权限
RUN if [ -f /app/docker-entrypoint.sh ]; then \
        chmod +x /app/docker-entrypoint.sh; \
    fi

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser -m appuser && \
    chown -R appuser:appuser /app

# 暴露Streamlit默认端口
EXPOSE 8501

# 切换到非root用户
USER appuser

# 设置启动脚本权限
RUN chmod +x /app/start_optimized.py

# 设置入口点和启动命令
ENTRYPOINT ["/app/docker-entrypoint.sh"]
CMD ["python", "/app/start_optimized.py"]
