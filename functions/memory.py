"""
    memory.py
"""
import os
import re
import logging
import streamlit as st
from typing import Optional, Tuple, List
from core.memory import LabelManager, Memory, Label
from core.memory import MemoryManager
from utils.uris import analyze_uris_subdirs_files

from openai import OpenAI

def memory_normalize(uri_prefix: str = "") -> Tuple[bool, str]:
    """
    治理符合 uri_prefix 的记忆，使其符合 Memory 规范
    输入：
        uri_prefix，必需是完整的一个路径，比如 /path/to/dir/ 或者 /path/to/file.md
    
    Returns:
        tuple: (success, result)，成功状态和结果信息
    """
    memory_manager = st.session_state.get('memory_manager')
    if not memory_manager:
        return False, "Memory manager not found in session state"

    if uri_prefix.endswith(".md"):
        summary, _ = memory_normalize_file(uri_prefix, memory_manager)
        return True, f"Processed memories in file {uri_prefix} summary: {summary}"
    
    elif uri_prefix.endswith("/"):
        summary, _ = memory_normalize_dir(uri_prefix, memory_manager)
        return True, f"Processed memories in directory {uri_prefix} summary: {summary}"
    
    else:
        return False, "Invalid uri_prefix, must end with '/' or '.md'"


def memory_normalize_file(file_uri: str, memory_manager: MemoryManager) -> Tuple[str, List[Label]]:
    """
    治理单个文件的记忆，使其符合 Memory 规范
    """
    # 文件级别
    label_manager = LabelManager()
    desc_memory = None
    label_memory = None
    results = memory_manager.search_memory(query=file_uri, k=10_000, search_type="uri")
    for mem in results:
        if 'label' in mem.labels:
            label_manager = LabelManager.from_markdown(mem.original_text)
            label_memory = mem
            break
    
    for mem in results:
        for lab in mem.labels:
            if lab not in label_manager.labels:
                memory_manager.add_label(lab, lab)
        
        if mem.summary == "":
            # 先检查是否都是完整的 MB，检查方法为 summary 是否为空
            summary, label_str, _ = generate_summary_and_labels(mem.original_text, label_manager.to_markdown())
            labels = LabelManager.from_markdown(label_str).labels.values()
            label_manager.extend(labels)
            new_labels = [x.label for x in labels]
            memory_manager.update_memory(mem.id, new_summary=summary, new_labels=new_labels)

        if 'description' in mem.labels:
            desc_memory = mem
    
    if desc_memory is None:
        # 没有就给他生成一个
        original_text = "文件内包含的内容：\n" + "\n  - ".join([mem.summary for mem in results])
        summary, label_str, _ = generate_summary_and_labels(original_text, label_manager.to_markdown())
        labels = LabelManager.from_markdown(label_str).labels.values()
        label_manager.extend(labels)
        new_labels = [x.label for x in labels] + ['description']
        memory_manager.add_memory(original_text, summary, new_labels, save_path=file_uri)
    else:
        summary = desc_memory.summary
    
    # 更新 label 记忆
    if label_memory:
        if label_memory.original_text != label_manager.to_markdown():
            memory_manager.update_memory(label_memory.id, new_text=label_manager.to_markdown())
    else:
        memory_manager.add_memory(label_manager.to_markdown(), "标签列表", ["label"], save_path=file_uri)

    return summary, label_manager.labels.values()

def memory_normalize_dir(dir_uri: str, memory_manager: MemoryManager) -> Tuple[str, List[Label]]:
    """
    治理单个目录的记忆，使其符合 Memory 规范
    """
    # 文件级别
    label_manager = LabelManager()
    desc_memory = None
    label_memory = None
    summaries = []

    results = memory_manager.search_memory(query=dir_uri, k=10_000, search_type="uri")
    
    # 获取所有比该目录多一个等级的文件
    subdirs, files = analyze_uris_subdirs_files(results, dir_uri)
    if "description.md" in files:
        # 有 description.md 就用 description.md 作为描述
        mems = memory_manager.search_memory(query=dir_uri + "description.md", k=2, search_type="uri")
        for mem in mems:
            if "description" in mem.labels:
                desc_memory = mem
            if "label" in mem.labels:
                label_memory = mem
                label_manager = LabelManager.from_markdown(label_memory.original_text)
        
        files.remove("description.md")
    
    for file in files:
        summary, labels = memory_normalize_file(dir_uri + file, memory_manager)
        summaries.append(summary)
        label_manager.extend(labels)
    
    for subdir in subdirs:
        summary, labels = memory_normalize_dir(dir_uri + subdir + "/", memory_manager)
        summaries.append(summary)
        label_manager.extend(labels)
    
    if desc_memory is None:
        # 没有就给他生成一个
        original_text = f"目录{dir_uri}内包含的内容：\n" + "\n  - ".join([mem.summary for mem in results])
        summary, label_str, _ = generate_summary_and_labels(original_text, label_manager.to_markdown())
        labels = LabelManager.from_markdown(label_str).labels.values()
        label_manager.extend(labels)
        new_labels = [x.label for x in labels] + 'description'
        memory_manager.add_memory(original_text, summary, new_labels, save_path=dir_uri + "description.md")
    else:
        summary = desc_memory.summary

    if label_memory is None:
        memory_manager.add_memory(label_manager.to_markdown(), "标签汇总", ['label'], \
            save_path=dir_uri + "description.md")
    else:
        memory_manager.update_memory(label_memory.id, new_text=label_manager.to_markdown())
    return summary, label_manager.labels.values()


def generate_summary_and_labels(text: str, label_text: str, path_description: str = "") -> Tuple[str, str, str]:
    """
    生成摘要和标签
    """
    system_prompt = """你是一个 AI 助手，你的任务是生成文本摘要和标签。
请根据文本内容生成一个摘要，并根据摘要生成标签。
标签包括所有实体，项目名、模块名都算实体。比如，liannu 就是一个对用户有意义的实体，表示一个工程模块的名字
其实包含一些概念，比如"defination" 表示XX的定义
标签的意义在于将文本内容进行高度概括，比如，如果有一个文本标签是 'liannu' 'defination'，那么这个文本就表示 liannu 的定义


输出格式，使用 XML 的格式
- summary 也是类似的总结，只不过是文本的形式，可以包含100字左右
- labels 标签内是符合 markdown 表格格式的标签列表，格式如下：

<summary>此处是摘要</summary>
<labels>
| id  | label      | description                           | category | parent_id | synonyms |
| --- | ---------- | ------------------------------------- | -------- | --------- | -------- |
| 1   | memory     | 记忆相关                        | concept  |           |          |
| 2   | april      | 一个AI智能体项目                      | project  |           |          |
| 3   | idea       | 一些想法                              | concept  |           |          |
| 4   | defination | 定义相关                              | concept  |           |          |
</labels>
<path>建议保存的路径</path>

注意
1. 已有标签能表达的话尽量用已有的标签
2. 标签最好用一个英文单词（通常是实体），描述写中文
3. 路径即标签，例如 `dir/file.md` 则 dir 和 file 都是标签，所以要结合标签生成路径！ 
"""

    client = OpenAI(base_url=os.getenv("BASE_URL"), api_key=os.getenv("API_KEY"))
    content = f"文本内容：\n{text}\n\n已知的标签列表：\n{label_text}\n\n" \
            f"{path_description}\n\n"
    logging.info(f"Content: {content}")
    response = client.chat.completions.create(
        model=os.getenv("MODEL"),
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": content}
        ]
    )
    # 提取摘要和标签
    response = response.choices[0].message.content
    logging.info(f"Response: {response}")
    summary_match = re.search(r"<summary>(.*?)</summary>", response, re.DOTALL)
    labels_match = re.search(r"<labels>(.*?)</labels>", response, re.DOTALL)
    path_match = re.search(r"<path>(.*?)</path>", response, re.DOTALL)
    return summary_match.group(1), labels_match.group(1), path_match.group(1)


def _analyze_query(query: str, memory_manager: MemoryManager) -> List[dict]:
    """
    使用LLM分析query并生成搜索参数列表
    """
    system_prompt = """你是一个记忆检索分析器，你的任务是将自然语言查询转换为记忆搜索参数。

可用的搜索类型:
- uri: 按路径前缀匹配
- label: 按标签匹配
- keyword: 字符匹配

输出格式(XML):
<search_params>
    <param>
        <type>搜索类型: label, keyword 二选一</type>
        <query>搜索词</query>
        <limit>结果数量(可选，默认10)</limit>
    </param>
    ...
</search_params>"""

    # 获取当前标签上下文
    label_context = "\n已知标签:\n" + memory_manager.labels_description

    client = OpenAI(base_url=os.getenv("BASE_URL"), api_key=os.getenv("API_KEY"))
    response = client.chat.completions.create(
        model=os.getenv("MODEL"),
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": query + label_context}
        ]
    )

    content = response.choices[0].message.content

    # 匹配所有param块
    param_blocks = re.findall(r'<param>(.*?)</param>', content, re.DOTALL)
    params = []

    for block in param_blocks:
        # 提取每个字段
        search_type = re.search(r'<type>(.*?)</type>', block, re.DOTALL)
        search_query = re.search(r'<query>(.*?)</query>', block, re.DOTALL)
        search_limit = re.search(r'<limit>(.*?)</limit>', block, re.DOTALL)

        if search_type and search_query:
            param = {
                "type": search_type.group(1).strip(),
                "query": search_query.group(1).strip()
            }
            if search_limit:
                param["limit"] = int(search_limit.group(1).strip())
            params.append(param)
    
    return params