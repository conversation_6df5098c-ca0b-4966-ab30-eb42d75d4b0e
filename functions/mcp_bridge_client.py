"""
MCP桥接客户端 - 通过Node.js桥接服务连接远程Smithery MCP服务
"""
import asyncio
import aiohttp
import logging
from typing import Dict, Any, List


class BridgeMCPClient:
    """通过Node.js桥接服务连接远程MCP服务的客户端"""

    def __init__(self, server_config: Dict[str, Any]):
        """初始化桥接MCP客户端

        Args:
            server_config: 服务器配置
        """
        self.server_config = server_config
        self.session = None
        self.bridge_url = "http://localhost:3001"
        self.server_name = server_config.get("server_name", "")
        self.api_key = server_config.get("api_key", "")
        self.initialized = False

    async def connect(self) -> None:
        """连接到MCP服务"""
        import aiohttp
        
        # 创建HTTP会话
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        )
        
        # 准备连接参数
        connect_data = {
            "serverName": self.server_name,
            "apiKey": self.api_key,
            "config": {
                "profile_id": self.server_config.get("profile_id", ""),
                "server_config": self.server_config.get("config", {})
            }
        }
        
        try:
            # 连接到桥接服务
            async with self.session.post(
                f"{self.bridge_url}/connect",
                json=connect_data
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("success"):
                        self.initialized = True
                        logging.info(f"成功连接到MCP服务: {self.server_name}")
                    else:
                        raise Exception(f"桥接服务连接失败: {result.get('error', '未知错误')}")
                else:
                    text = await response.text()
                    raise Exception(f"桥接服务HTTP错误 {response.status}: {text}")
                    
        except Exception as e:
            if self.session:
                await self.session.close()
                self.session = None
            raise e

    async def disconnect(self) -> None:
        """断开连接"""
        if self.session:
            try:
                # 通知桥接服务断开连接
                async with self.session.post(
                    f"{self.bridge_url}/disconnect",
                    json={"serverName": self.server_name}
                ) as response:
                    pass  # 忽略响应
            except:
                pass  # 忽略错误
            
            await self.session.close()
            self.session = None
        self.initialized = False

    async def get_available_tools(self) -> List[Any]:
        """获取可用工具"""
        if not self.session or not self.initialized:
            raise RuntimeError("未连接到MCP服务")
        
        try:
            async with self.session.get(
                f"{self.bridge_url}/tools/{self.server_name}"
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("success"):
                        # 创建简单的工具对象
                        tools = []
                        for tool_data in result.get("tools", []):
                            tool = type('Tool', (), {
                                'name': tool_data.get('name', ''),
                                'description': tool_data.get('description', ''),
                                'inputSchema': tool_data.get('inputSchema', {})
                            })()
                            tools.append(tool)
                        return tools
                    else:
                        raise Exception(f"获取工具失败: {result.get('error', '未知错误')}")
                else:
                    text = await response.text()
                    raise Exception(f"HTTP错误 {response.status}: {text}")
                    
        except Exception as e:
            logging.error(f"获取MCP工具列表失败: {e}")
            return []

    def call_tool(self, tool_name: str):
        """调用工具"""
        if not self.session or not self.initialized:
            raise RuntimeError("未连接到MCP服务")

        async def callable(*args, **kwargs):
            try:
                call_data = {
                    "serverName": self.server_name,
                    "toolName": tool_name,
                    "arguments": kwargs
                }
                
                async with self.session.post(
                    f"{self.bridge_url}/call",
                    json=call_data
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if result.get("success"):
                            return result.get("result", "")
                        else:
                            raise Exception(f"工具调用失败: {result.get('error', '未知错误')}")
                    else:
                        text = await response.text()
                        raise Exception(f"HTTP错误 {response.status}: {text}")
                        
            except Exception as e:
                logging.error(f"MCP工具调用失败: {e}")
                raise e

        return callable


async def test_bridge_client():
    """测试桥接客户端"""
    config = {
        "server_name": "@nickclyde/duckduckgo-mcp-server",
        "api_key": "8b943f07-94c8-487e-954b-d6068a08e306",
        "profile_id": "",
        "config": {}
    }
    
    client = BridgeMCPClient(config)
    
    try:
        print("连接到MCP服务...")
        await client.connect()
        print("✅ 连接成功")
        
        print("获取可用工具...")
        tools = await client.get_available_tools()
        print(f"✅ 找到 {len(tools)} 个工具:")
        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")
        
        if tools:
            print("测试工具调用...")
            search_tool = client.call_tool("search")
            result = await search_tool(query="python programming")
            print(f"✅ 搜索结果: {result[:200]}...")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        await client.disconnect()
        print("🔌 连接已断开")


if __name__ == "__main__":
    asyncio.run(test_bridge_client())
