"""
MCP工具调用模块 - 通用实现
"""
import json
import logging
import os
import requests
from typing import Dict, <PERSON><PERSON>, Any, Optional
from urllib.parse import urljoin


def use_mcp_tool(server_name: str, tool_name: str, arguments: str) -> Tuple[bool, str]:
    """
    使用MCP服务器提供的工具

    Args:
        server_name: MCP服务器名称
        tool_name: 工具名称
        arguments: 工具参数，JSON格式字符串

    Returns:
        (成功状态, 结果消息)
    """
    # 检查MCP服务是否启用
    if not os.environ.get("MCP_ENABLED", "false").lower() == "true":
        return False, "MCP服务未启用，请在设置中启用MCP服务"

    # 获取MCP配置
    try:
        mcp_config = json.loads(os.environ.get("MCP_CONFIG", "{}"))
    except json.JSONDecodeError:
        return False, "MCP配置格式错误，请检查配置"

    # 检查服务器是否在配置中
    if server_name not in mcp_config:
        return False, f"未找到MCP服务器配置: {server_name}"

    server_config = mcp_config.get(server_name, {})
    server_url = server_config.get("url")

    if not server_url:
        return False, f"MCP服务器 {server_name} 未配置URL"

    # 解析参数
    try:
        args = json.loads(arguments) if isinstance(arguments, str) else arguments
    except json.JSONDecodeError:
        return False, f"参数解析失败: {arguments}"

    # 获取服务器配置的其他参数
    method = server_config.get("method", "POST").upper()  # 默认使用POST
    headers = {
        "Content-Type": "application/json",
    }

    # 添加认证信息
    api_key = server_config.get("api_key", "")
    auth_type = server_config.get("auth_type", "bearer").lower()

    if api_key:
        if auth_type == "bearer":
            headers["Authorization"] = f"Bearer {api_key}"
        elif auth_type == "api-key":
            headers["X-API-Key"] = api_key

    # 添加自定义头部
    custom_headers = server_config.get("headers", {})
    headers.update(custom_headers)

    # 构建请求URL
    # 确保server_url以/结尾，tool_name不以/开头
    if not server_url.endswith("/"):
        server_url += "/"
    if tool_name.startswith("/"):
        tool_name = tool_name[1:]

    request_url = urljoin(server_url, tool_name)

    # 构建请求
    try:
        # 根据HTTP方法发送请求
        if method == "GET":
            response = requests.get(
                request_url,
                headers=headers,
                params=args,
                timeout=server_config.get("timeout", 60)
            )
        elif method == "POST":
            response = requests.post(
                request_url,
                headers=headers,
                json=args,
                timeout=server_config.get("timeout", 60)
            )
        elif method == "PUT":
            response = requests.put(
                request_url,
                headers=headers,
                json=args,
                timeout=server_config.get("timeout", 60)
            )
        elif method == "DELETE":
            response = requests.delete(
                request_url,
                headers=headers,
                json=args,
                timeout=server_config.get("timeout", 60)
            )
        else:
            return False, f"不支持的HTTP方法: {method}"

        # 检查响应
        if response.status_code >= 200 and response.status_code < 300:
            # 尝试解析JSON响应
            try:
                result = response.json()
                return True, json.dumps(result, ensure_ascii=False, indent=2)
            except json.JSONDecodeError:
                # 如果不是JSON，返回文本
                return True, response.text
        else:
            return False, f"MCP服务器请求失败: {response.status_code} - {response.text}"

    except Exception as e:
        logging.error(f"MCP工具调用异常: {str(e)}")
        return False, f"MCP工具调用异常: {str(e)}"