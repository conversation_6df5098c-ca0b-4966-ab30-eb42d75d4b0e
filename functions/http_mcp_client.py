"""
HTTP MCP客户端 - 使用Python MCP SDK连接远程Smithery服务
"""
import asyncio
import logging
from typing import Dict, Any, List
from urllib.parse import urlencode

# 导入MCP SDK的HTTP客户端
try:
    from mcp.client.streamable_http import streamablehttp_client
    from mcp import ClientSession
    MCP_HTTP_AVAILABLE = True
except ImportError:
    MCP_HTTP_AVAILABLE = False
    logging.warning("MCP HTTP客户端不可用，请升级mcp包: pip install 'mcp[cli]>=1.8.0'")


class HTTPMCPClient:
    """HTTP MCP客户端，使用Python MCP SDK连接远程Smithery服务"""

    def __init__(self, server_config: Dict[str, Any]):
        """初始化HTTP MCP客户端

        Args:
            server_config: 服务器配置
        """
        self.server_config = server_config
        self.session = None
        self._client = None
        self.initialized = False
        self.server_name = server_config.get("server_name", "")
        self.api_key = server_config.get("api_key", "")
        
        if not MCP_HTTP_AVAILABLE:
            raise RuntimeError("MCP HTTP客户端不可用，请升级mcp包")

    def _build_smithery_url(self) -> str:
        """构建Smithery服务URL"""
        profile_id = self.server_config.get("profile_id", "")
        config = self.server_config.get("config", {})
        
        # 构建基础URL
        base_url = f"https://server.smithery.ai/{self.server_name}/mcp"
        
        # 构建查询参数
        params = {"api_key": self.api_key}
        
        if profile_id:
            params["profile"] = profile_id
        else:
            # 添加手动配置参数
            params.update(config)
        
        # 构建完整URL
        query_string = urlencode(params)
        return f"{base_url}?{query_string}"

    async def connect(self) -> None:
        """连接到HTTP MCP服务器"""
        try:
            # 构建Smithery URL
            url = self._build_smithery_url()
            logging.info(f"连接到Smithery MCP服务: {url}")
            
            # 使用MCP SDK的HTTP客户端
            self._client_context = streamablehttp_client(url)
            read_stream, write_stream, _ = await self._client_context.__aenter__()
            
            # 创建客户端会话
            self.session = ClientSession(read_stream, write_stream)
            await self.session.__aenter__()
            
            # 初始化连接
            await self.session.initialize()
            
            self.initialized = True
            logging.info(f"成功连接到MCP服务: {self.server_name}")
            
        except Exception as e:
            logging.error(f"连接MCP服务失败: {e}")
            await self.disconnect()
            raise e

    async def disconnect(self) -> None:
        """断开HTTP连接"""
        try:
            if self.session:
                await self.session.__aexit__(None, None, None)
                self.session = None
            
            if hasattr(self, '_client_context'):
                await self._client_context.__aexit__(None, None, None)
                delattr(self, '_client_context')
                
        except Exception as e:
            logging.warning(f"断开连接时出错: {e}")
        
        self.initialized = False

    async def get_available_tools(self) -> List[Any]:
        """获取可用工具"""
        if not self.session or not self.initialized:
            raise RuntimeError("未连接到MCP服务器")
        
        try:
            # 使用MCP SDK获取工具列表
            tools_result = await self.session.list_tools()
            
            # 转换为兼容格式
            tools = []
            for tool_data in tools_result.tools:
                tool = type('Tool', (), {
                    'name': tool_data.name,
                    'description': tool_data.description,
                    'inputSchema': tool_data.inputSchema
                })()
                tools.append(tool)
            
            logging.info(f"获取到 {len(tools)} 个工具: {[t.name for t in tools]}")
            return tools
            
        except Exception as e:
            logging.error(f"获取MCP工具列表失败: {e}")
            return []

    def call_tool(self, tool_name: str):
        """调用工具"""
        if not self.session or not self.initialized:
            raise RuntimeError("未连接到MCP服务器")

        async def callable(*args, **kwargs):
            try:
                # 使用MCP SDK调用工具
                result = await self.session.call_tool(tool_name, arguments=kwargs)
                
                # 提取文本内容
                content = ""
                if result.content:
                    for item in result.content:
                        if hasattr(item, 'text'):
                            content += item.text
                        elif hasattr(item, 'data'):
                            content += str(item.data)
                        else:
                            content += str(item)
                
                logging.info(f"工具调用成功: {tool_name}")
                return content or str(result)
                
            except Exception as e:
                logging.error(f"MCP工具调用失败: {e}")
                raise e

        return callable


async def test_http_mcp_client():
    """测试HTTP MCP客户端"""
    config = {
        "server_name": "@nickclyde/duckduckgo-mcp-server",
        "api_key": "8b943f07-94c8-487e-954b-d6068a08e306",
        "profile_id": "",
        "config": {}
    }
    
    client = HTTPMCPClient(config)
    
    try:
        print("🔗 连接到MCP服务...")
        await client.connect()
        print("✅ 连接成功")
        
        print("📋 获取可用工具...")
        tools = await client.get_available_tools()
        print(f"✅ 找到 {len(tools)} 个工具:")
        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")
        
        if tools:
            print("🔧 测试工具调用...")
            search_tool = client.call_tool("search")
            result = await search_tool(query="python programming")
            print(f"✅ 搜索结果: {result[:200]}...")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await client.disconnect()
        print("🔌 连接已断开")


if __name__ == "__main__":
    asyncio.run(test_http_mcp_client())
