[server]
# 服务器配置
port = 8501
address = "0.0.0.0"
maxUploadSize = 200
enableCORS = false
enableXsrfProtection = false

# 性能优化
maxMessageSize = 200
allowRunOnSave = false
runOnSave = false
fileWatcherType = "none"

# 禁用开发模式功能以减少资源消耗
developmentMode = false

[global]
# 全局配置
developmentMode = false
logLevel = "warning"
unitTest = false

# 禁用遥测
disableTelemetry = true

[browser]
# 浏览器配置
gatherUsageStats = false
showErrorDetails = false

[theme]
# 主题配置
base = "light"

[runner]
# 运行器配置
magicEnabled = false
installTracer = false
fixMatplotlib = false

# 减少内存使用
enforceSerializableSessionState = true
