# 构建阶段：安装依赖
FROM python:3.12 AS builder

# 设置pip镜像源
ARG PIP_INDEX_URL="https://pypi.tuna.tsinghua.edu.cn/simple"
ENV PIP_INDEX_URL=${PIP_INDEX_URL}

WORKDIR /app

# 安装构建依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    git \
    curl \
    # matplotlib依赖
    libfreetype6-dev \
    pkg-config \
    # 其他可能需要的依赖
    libxml2-dev \
    libxslt1-dev \
    zlib1g-dev \
    libjpeg-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir --retries 5 --timeout 60 -r requirements.txt

# 最终阶段：运行应用
FROM python:3.12

WORKDIR /app

# 安装运行时依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    # 运行时必要的库
    libgomp1 \
    libfreetype6 \
    libjpeg62-turbo \
    zlib1g \
    # 健康检查需要
    curl \
    # 权限管理需要
    gosu \
    # 文件类型检测需要
    file \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 从构建阶段复制Python环境并确保正确权限
COPY --from=builder /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# 确保权限正确
RUN chmod -R 755 /usr/local/bin && \
    find /usr/local/lib/python3.12/site-packages -type d -exec chmod 755 {} \; && \
    find /usr/local/lib/python3.12/site-packages -type f -exec chmod 644 {} \; && \
    # 确保特定的可执行文件有执行权限
    find /usr/local/bin -type f -name "pip*" -exec chmod 755 {} \; && \
    find /usr/local/bin -type f -name "python*" -exec chmod 755 {} \; && \
    find /usr/local/bin -type f -name "streamlit" -exec chmod 755 {} \; && \
    # 检查并修复任何可能的权限问题
    find /usr/local/bin -type f -exec sh -c 'file "{}" | grep -q "executable" && chmod +x "{}" || true' \;

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser -m appuser

# 复制应用代码
COPY . .

# 设置入口点脚本
RUN cp /app/docker-entrypoint.sh /usr/local/bin/ && \
    chmod +x /usr/local/bin/docker-entrypoint.sh

# 暴露Streamlit默认端口
EXPOSE 8501

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV HOME=/home/<USER>


# 设置入口点
ENTRYPOINT ["docker-entrypoint.sh"]

# 启动应用
CMD ["streamlit", "run", "app.py", "--server.port=8501", "--server.address=0.0.0.0"]


