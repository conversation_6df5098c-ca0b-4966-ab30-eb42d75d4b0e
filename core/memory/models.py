"""
    models.py
"""
import os
from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional, Any, Dict, Tuple
import numpy as np
import json
import re


@dataclass
class Memory:
    """ 记忆片段 """
    id: int
    original_text: str
    summary: str
    uri: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    labels: List[str] = field(default_factory=list)
    embedding: Optional[np.ndarray] = None
    metadata: Optional[dict] = field(default_factory=dict)

    def update(self, new_text: Optional[str] = None,
               new_summary: Optional[str] = None, labels: Optional[List[str]] = None,
               metadata: Optional[dict] = None, uri: Optional[str] = None):
        """ 更新记忆 """
        if new_text is not None:
            self.original_text = new_text
        if new_summary is not None:
            self.summary = new_summary
        if labels is not None:
            self.labels = labels
        if metadata is not None:
            self.metadata = metadata
        if uri is not None:
            self.uri = uri
        self.updated_at = datetime.now()
    
    @classmethod
    def from_markdown(cls: 'Memory', markdown: str) -> 'Memory':
        """从Markdown格式的字符串中解析记忆片段"""
        # 使用正则表达式提取数据
        id_match = re.search(r"<id>(.*?)</id>", markdown)
        created_at_match = re.search(r"<created_at>(.*?)</created_at>", markdown)
        updated_at_match = re.search(r"<updated_at>(.*?)</updated_at>", markdown)
        labels_match = re.search(r"<labels>(.*?)</labels>", markdown)
        summary_match = re.search(r"<summary>(.*?)</summary>", markdown, re.DOTALL)
        original_text_match = re.search(r"<original_text>(.*?)</original_text>", markdown, re.DOTALL)
        metadata_match = re.search(r"<metadata>\n```json\n(.*?)\n```\n</metadata>", markdown, re.DOTALL)

        # 解析提取到的数据
        return cls(
            id=int(id_match.group(1)) if id_match else -1,
            created_at=created_at_match.group(1) if created_at_match else datetime.now(),
            updated_at=updated_at_match.group(1) if updated_at_match else datetime.now(),
            labels=[x.strip() for x in labels_match.group(1).split(',')] if labels_match else [],
            summary=summary_match.group(1) if summary_match else '',
            original_text=original_text_match.group(1).strip() if original_text_match else markdown,
            metadata=json.loads(metadata_match.group(1)) if metadata_match else {}
        )

    def to_markdown(self, ignore_metadata: bool = False) -> str:
        """将记忆片段转换为Markdown格式"""
        md = []
        # 添加元数据
        md.append(f"<id>{self.id}</id>\n")
        md.append(f"<created_at>{self.created_at}</created_at>\n")
        md.append(f"<updated_at>{self.updated_at}</updated_at>\n")

        # 添加标签
        md.append(f"<labels>{', '.join(self.labels)}</labels>\n")
    
        # 添加摘要
        md.append(f"<summary>{self.summary}</summary>\n")
        
        # 添加原始文本
        md.append("<original_text>\n")
        md.append(self.original_text)
        md.append("\n</original_text>\n")
        
        if self.metadata and not ignore_metadata:
            md.append("<metadata>\n\n```json\n" + \
                        json.dumps(self.metadata, ensure_ascii=False, indent=4) + \
                        "\n```\n</metadata>\n")
        
        return "\n".join(md)
    

@dataclass
class Label:
    """ 标签 """
    id: int = field(default=-1)
    label: str = field(default="")
    description: str = field(default="")
    category: str = field(default="default")
    synonyms: List[str] = field(default_factory=list)
    parent_id: Optional[int] = None
    children_ids: List[int] = field(default_factory=list)

    def update(self, new_label: Optional[str] = None,
               description: Optional[str] = None, category: Optional[str] = None,
               synonyms: List[str] = field(default_factory=list)):
        """ 更新标签 """
        if new_label is not None:
            self.label = new_label
        if description is not None:
            self.description = description
        if category is not None:
            self.category = category
        if synonyms is not None:
            self.synonyms = synonyms

    def to_markdown(self) -> str:
        """ 将标签转换为Markdown格式表格中的一行 """
        return f"| {self.id} | {self.label} | {self.description} | {self.category} | {self.parent_id}" \
            f" | {', '.join(self.synonyms)} |"

    @classmethod
    def from_markdown(cls: 'Label', line: str) -> 'Label':
        """ 从Markdown格式的字符串中解析标签 """
        # 解析表格行
        cls = Label()
        parts = [part.strip() for part in line.strip('|').split('|')]
        if len(parts) < 4:  # 至少需要ID、标签、描述和类别
            raise ValueError("Invalid label format")

        
        # 提取标签属性
        label_id = int(parts[0])
        label_name = parts[1]
        description = parts[2]
        category = parts[3]
        parent_name = parts[4]
        synonyms = parts[5] if len(parts) > 5 else "" # 提取可选的同义词
        
        cls.id = label_id
        cls.label = label_name
        cls.description = description
        cls.category = category
        cls.parent_id = parent_name
        cls.synonyms = [x.strip() for x in synonyms.split(',')] if synonyms else []

        # 解析提取到的数据
        return cls


@dataclass
class LabelManager:
    """
        标签管理器
    """
    labels: Dict[str, Label] = field(default_factory=dict) # Key 为 label 名称
    num: int = field(default=0)
    next_label_id: int = field(default=1)

    @classmethod
    def from_markdown(cls: 'LabelManager', markdown: str) -> 'LabelManager':
        """ 从Markdown 表格格式中解析标签管理器
        """
        cls = LabelManager()
        # 按行解析表格内容
        lines = markdown.strip().split('\n')
        if len(lines) < 3:  # 至少需要表头、分隔符和一行数据
            return cls
        
        # 跳过表头和分隔符
        for line in lines[2:]:
            if not line.strip():
                continue

            try:
                # 创建Label对象
                label = Label().from_markdown(line)
                
                # 更新labels字典和next_label_id
                cls.labels[label.label] = label
                cls.num = cls.num + 1
            except (ValueError, AttributeError, TypeError) as e:
                print(f"Warning: Failed to parse label definition: {line}. Error: {str(e)}")
        return cls

    def add(self, label: str, description: str, category: str = 'default', synonyms: str = "") -> None:
        """ 添加标签 """
        if label in self.labels:
            return
        
        # 创建新的标签对象
        new_label = Label(
            id=self.next_label_id,
            label=label,
            description=description,
            category=category,
            synonyms=[x.strip() for x in synonyms.split(',')] if synonyms else []
        )
        
        # 更新标签管理器
        self.labels[label] = new_label
        self.num += 1
        self.next_label_id += 1

    def delete(self, label_name: str) -> None:
        """ 删除标签 """
        if label_name in self.labels:
            del self.labels[label_name]
            self.num -= 1
    
    def extend(self, labels: List[Label]) -> None:
        """ 批量添加标签
        """
        for label in labels:
            self.add(label.label, label.description, label.category, ', '.join(label.synonyms))

    def update(self, label_name: str, new_label: Optional[str] = None, new_description: Optional[str] = None, \
                new_category: Optional[str] = None, new_synonyms: Optional[List[str]] = None) -> None:
        """ 更新标签 """
        if label_name not in self.labels:
            raise ValueError(f"Label '{label_name}' does not exist.")
        
        label = self.labels[label_name]
        label.update(
            new_label=new_label,
            description=new_description,
            category=new_category,
            synonyms=new_synonyms
        )

    def to_markdown(self) -> str:
        """ 将标签管理器转换为Markdown 表格格式
        """
        # 构建表格头部
        table_header = "| ID | 标签 | 描述 | 类别 | 父标签ID | 同义词 |"
        table_separator = "|-----|------|------|------|----------|--------|"
        return "\n".join([table_header, table_separator] + [label.to_markdown() for label in self.labels.values()])
    