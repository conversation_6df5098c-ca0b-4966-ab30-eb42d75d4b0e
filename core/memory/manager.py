"""
    manager.py
"""
import os
import json
import numpy as np
import openai
from typing import List, Optional, Dict, Any
from .models import Memory, Label, LabelManager
from .doc_storage import DocMemoryHandler


class MemoryManager:
    """
        数据管理层，增删改查，治理
    """
    def __init__(self, root_dir: str = "./data/memory4doc"):
        """
        初始化记忆管理器
        """
        self.dimension = 1536                       # OpenAI ada-002 embedding dimension
        with open("id.json") as f:
            ids = json.load(f)
        self.next_memory_id = ids['memory_id']      # 记忆ID计数器
        self.next_label_id = ids['label_id']        # 标签ID计数器
        self._root_memory_dir = root_dir            # 存储路径
        self._memory_data: List[Memory] = []        # 存储记忆
        self._label_manager: LabelManager = None     # 标签管理器
        self.handler = DocMemoryHandler()   # 文档持久化处理
        # self.index = faiss.IndexFlatL2(self.dimension)

    def load_memory(self, root_dir: str = "./data/memory4doc/"):
        """加载记忆
        
        Args:
            root_dir: 根目录路径
        """
        self._root_memory_dir = root_dir
        self._label_manager = LabelManager()
        self._label_manager.next_label_id = self.next_label_id
        self._memory_data, labels = self.handler.load(root_dir)
        for label in labels:
            if label.id < 0:
                label.id = self.next_label_id
                self.next_label_id += 1
            else:
                self.next_label_id = max(self.next_label_id, label.id + 1)
        
        self._label_manager.extend(labels)

        for mem in self._memory_data:
            if mem.id < 0:
                mem.uri = mem.uri.replace(f"id={mem.id}", f"id={self.next_memory_id}")
                mem.id = self.next_memory_id
                self.next_memory_id += 1
            else:
                self.next_memory_id = max(self.next_memory_id, mem.id + 1)

            # 更新记忆里的标签ID
            if 'label' in mem.labels:
                _tp_lab_man = LabelManager.from_markdown(mem.original_text)
                for label, obj in _tp_lab_man.labels.items():
                    if label in self._label_manager.labels:
                        # 如果标签已存在，更新ID
                        obj.id = self._label_manager.labels[label].id
                    
                mem.original_text = _tp_lab_man.to_markdown()

    def _save_ids(self):
        with open("id.json", "w") as f:
            json.dump({
                'memory_id': self.next_memory_id,
                'label_id': self._label_manager.next_label_id
            }, f)

    def save_memory(self):
        """保存记忆
        """
        self._save_ids()
        self.handler.save(self._memory_data)
 
    def _get_embedding(self, text: str) -> np.ndarray:
        """获取文本的向量嵌入"""
        client = openai.OpenAI(
            base_url=os.getenv("EMBEDDING_BASE_URL"),
            api_key=os.getenv("EMBEDDING_API_KEY")
        )
        response = client.embeddings.create(
            input=text,
            model="text-embedding-ada-002"
        )
        return np.array(response.data[0].embedding, dtype=np.float32)
 
    def search_memory(self, query: str, labels: List[str] = None, k: int = 5, \
        search_type: str = "keyword") -> List[Memory]:
        """
            Search memories using different methods
        """
        if search_type == "uri":
            matches = [mem for mem in self._memory_data
                      if mem.uri.lower().startswith(query.lower())]
            return matches[::-1][:k]

        if search_type == "keyword":
            matches = [mem for mem in self._memory_data
                      if query.lower() in mem.original_text.lower() or 
                         query.lower() in mem.summary.lower()]
            return matches[::-1][:k]
        
        elif search_type == "label":
            if labels is None or not labels:
                return []
            
            # 举例：labels 是 A、B、C，mem 是 A、B、C、D，则 mem 可召回
            print(labels)
            matches = [mem for mem in self._memory_data
                      if all(lad in mem.labels for lad in labels)]
            return matches[::-1][:k]
        
        else:
            raise ValueError(f"Invalid search type: {search_type}")
 
    def add_memory(self, memory_text: str, summary: str, labels: List[str] = None, metadata: Dict[str, Any] = {}
                   , save_path: str = None):
        """
            添加新的记忆
        """
        embedding = None  # self._get_embedding(summary)

        uri = save_path + "?" + "&".join([f"label={label}" for label in labels] + [f"id={self.next_memory_id}"])

        memory = Memory(
            id=self.next_memory_id,
            original_text=memory_text,
            summary=summary,
            labels=labels,
            embedding=embedding,
            metadata=metadata,
            uri=uri
        )

        self._memory_data.append(memory)
        self.next_memory_id += 1

    def update_memory(self, memory_id: int, new_text: str = None, new_summary: str = None,
                      new_labels: List[str] = None, new_metadata: str = None, save_path: str = None):
        """Update an existing memory
        
        Args:
            memory_id: ID of memory to update
            new_text: New text content
            new_summary: New summary
            new_label_ids: New label_ids
            new_metadata: New metadata
        """
        for mem in self._memory_data:
            if mem.id == memory_id:
                # 确保label_ids不重复
                if new_labels is not None:
                    new_labels = list(set(new_labels))
                else:
                    new_labels = mem.labels
                
                if save_path is None:
                    save_path = mem.uri.split("?")[0]
                uri = save_path + "?" + "&".join([f"label={label}" for label in new_labels] + [f"id={mem.id}"])
                
                if new_metadata is not None:
                    new_metadata = json.loads(new_metadata)
                
                mem.update(new_text, new_summary, new_labels, new_metadata, uri)                
                return
 
        raise ValueError(f"Memory with id {memory_id} not found")
 
    def delete_memory(self, memory_id: str):
        """Delete a memory by ID
        
        Args:
            memory_id: ID of memory to delete
        """
        for i, mem in enumerate(self._memory_data):
            if mem.id == memory_id:       
                del self._memory_data[i]
                return
 
        raise ValueError(f"Memory with id {memory_id} not found")
 
    def add_label(self, label: str, description: str, category: str = "default", 
                  synonyms: str = ""):
        """添加新label
        
        Args:
            label: 标签名称
            description: 标签描述
            category: 标签类别
            synonyms: 同义词用逗号分隔
        """
        # Ensure category is a string and parent_id is either None or int
        category = str(category) if category is not None else "default"
        
        self._label_manager.add(
            label=label,
            description=description,
            category=category,
            synonyms=synonyms.split(",") if synonyms else []
        )

    def update_label(self, old_label: str, new_label: str, new_description: str = None,
                     new_category: str = None, new_synonyms: str = "",
                     new_parent_id: Optional[int] = None):
        """Update a label and its description
        
        Args:
            old_label: 原标签名称
            new_label: 新标签名称
            new_description: 新描述
            new_category: 新类别
            new_synonyms: 新同义词列表
            new_parent_id: 新父标签ID
        """
        label_obj = self._label_manager.labels.get(old_label)
        if label_obj is None:
            raise ValueError(f"Label '{old_label}' not found")
        
        label_obj.update(new_label, new_description, new_category, new_synonyms)

        # 所有涉及的记忆的标签都要修改，还要修改 标签描述 的 MB
        for memory in self._memory_data:
            if old_label != new_label and old_label in memory.labels:
                memory.labels.remove(old_label)
                memory.labels.append(new_label)
            
            if 'label' in memory.labels:
                _tp_lab_man = LabelManager().from_markdown(memory.original_text)
                if old_label in _tp_lab_man.labels:
                    _ = _tp_lab_man.labels.pop(old_label)
                    _tp_lab_man.labels[new_label] = label_obj
                    memory.original_text = _tp_lab_man.to_markdown()

    def delete_label(self, label: str) -> Dict[str, str]:
        """Delete a label and remove it from all memories"""
        # 删除标签
        label_obj = self._label_manager.labels.pop(label)
        if label_obj is None:
            raise ValueError(f"Label '{label}' not found")
        
        # 删除所有记忆中这个标签
        for memory in self._memory_data:
            if label in memory.labels:
                memory.labels.remove(label)

            if 'label' in memory.labels:
                _tp_lab_man = LabelManager().from_markdown(memory.original_text)
                if label in _tp_lab_man.labels:
                    _tp_lab_man.delete(label)
                    memory.original_text = _tp_lab_man.to_markdown()
        
        return {"success": True, "message": "Label deleted successfully!"}

    def list_labels(self) -> List[Dict[str, Any]]:
        """列举标签（标签名、描述、关联关系、同义词、Label的领域）"""
        return [
            {
                "id": label.id,
                "name": label.label,
                "description": label.description,
                "category": label.category,
                "synonyms": label.synonyms,
                "parent_id": label.parent_id,
                "children_ids": label.children_ids
            }
            for label in self._label_manager.labels.values()
        ]
    
    @property
    def labels_description(self) -> str:
        """
            labels_description
        """
        return self._label_manager.to_markdown()

    @property
    def path_description(self) -> str:
        """
            获取所有记忆的路径
        """
        paths = set()
        for mem in self._memory_data:
            path = mem.uri.split("?")[0]
            paths.add(path)
        
        return "已有文件路径信息：\n" + "\n- ".join(list(paths))

if __name__ == "__main__":
    # 示例使用
    manager = MemoryManager()
    output_dir = "data/memory4doc"
    
    # 导出标签信息为单个 memory 文件
    print("\nExporting labels as memory...")
    try:
        manager.export_labels_as_memory(output_dir)
        print("Successfully exported labels")
    except Exception as e:
        print(f"Failed to export labels: {str(e)}")
    
    # 导出所有记忆
    print("\nExporting memories...")
    for memory in manager.memory_data:
        try:
            manager.export_memory_to_markdown_file(memory, output_dir)
            print(f"Successfully exported memory {memory.id}")
        except Exception as e:
            print(f"Failed to export memory {memory.id}: {str(e)}")
