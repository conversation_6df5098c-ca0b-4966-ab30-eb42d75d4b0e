#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的Streamlit应用启动脚本
解决线程资源耗尽问题
"""
import os
import sys
import threading
import gc
import signal
import logging
from concurrent.futures import ThreadPoolExecutor

# 设置线程限制
threading.stack_size(32768)  # 32KB stack size per thread

# 设置环境变量
os.environ.update({
    'PYTHONUNBUFFERED': '1',
    'STREAMLIT_SERVER_ENABLE_CORS': 'false',
    'STREAMLIT_SERVER_ENABLE_XSRF_PROTECTION': 'false',
    'STREAMLIT_GLOBAL_DEVELOPMENT_MODE': 'false',
    'STREAMLIT_SERVER_FILE_WATCHER_TYPE': 'none',
    'STREAMLIT_SERVER_RUN_ON_SAVE': 'false',
    'STREAMLIT_BROWSER_GATHER_USAGE_STATS': 'false',
    'STREAMLIT_GLOBAL_DISABLE_TELEMETRY': 'true',
    'STREAMLIT_RUNNER_MAGIC_ENABLED': 'false',
    'STREAMLIT_RUNNER_INSTALL_TRACER': 'false',
    'STREAMLIT_RUNNER_FIX_MATPLOTLIB': 'false',
})

# 配置日志以减少输出
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 全局线程池，限制并发线程数
_thread_pool = ThreadPoolExecutor(max_workers=4)

def cleanup_resources():
    """清理资源"""
    try:
        _thread_pool.shutdown(wait=False)
        gc.collect()
    except Exception as e:
        print(f"清理资源时出错: {e}")

def signal_handler(signum, frame):
    """信号处理器"""
    print("接收到退出信号，正在清理资源...")
    cleanup_resources()
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

if __name__ == "__main__":
    try:
        # 导入并启动Streamlit应用
        import streamlit.web.cli as stcli
        
        # 设置命令行参数
        sys.argv = [
            "streamlit",
            "run",
            "app.py",
            "--server.port=8501",
            "--server.address=0.0.0.0",
            "--global.developmentMode=false",
            "--server.maxUploadSize=200",
            "--server.enableCORS=false",
            "--server.enableXsrfProtection=false",
            "--server.fileWatcherType=none",
            "--server.runOnSave=false",
            "--browser.gatherUsageStats=false",
            "--global.disableTelemetry=true",
            "--runner.magicEnabled=false",
            "--runner.installTracer=false",
            "--runner.fixMatplotlib=false"
        ]
        
        # 启动应用
        stcli.main()
        
    except KeyboardInterrupt:
        print("应用被用户中断")
    except Exception as e:
        print(f"启动应用时出错: {e}")
    finally:
        cleanup_resources()
