#!/usr/bin/env node

/**
 * MCP桥接服务 - 连接Python应用和Smithery MCP服务
 * 使用官方Node.js MCP SDK
 */

import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";
import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 3001;

// 中间件
app.use(cors());
app.use(express.json());

// 存储活跃的MCP客户端连接
const mcpClients = new Map();

/**
 * 连接到Smithery MCP服务
 */
async function connectToMCPServer(serverName, apiKey, config = {}) {
    try {
        console.log(`连接到MCP服务: ${serverName}`);
        
        // 构建Smithery URL
        let url;
        if (config.profile_id) {
            url = `https://server.smithery.ai/${serverName}/mcp?profile=${config.profile_id}&api_key=${apiKey}`;
        } else {
            // 手动配置
            const configParams = new URLSearchParams(config.server_config || {});
            url = `https://server.smithery.ai/${serverName}/mcp?${configParams.toString()}&api_key=${apiKey}`;
        }
        
        console.log(`连接URL: ${url}`);
        
        // 创建传输层
        const transport = new StreamableHTTPClientTransport(url);
        
        // 创建客户端
        const client = new Client({
            name: "efficiency-mcp-bridge",
            version: "1.0.0"
        });
        
        // 连接
        await client.connect(transport);
        
        // 存储客户端
        mcpClients.set(serverName, client);
        
        console.log(`✅ 成功连接到 ${serverName}`);
        return { success: true, message: `成功连接到 ${serverName}` };
        
    } catch (error) {
        console.error(`❌ 连接失败 ${serverName}:`, error.message);
        return { success: false, error: error.message };
    }
}

/**
 * 获取MCP服务的可用工具
 */
async function getAvailableTools(serverName) {
    try {
        const client = mcpClients.get(serverName);
        if (!client) {
            throw new Error(`未找到服务连接: ${serverName}`);
        }
        
        const result = await client.listTools();
        console.log(`📋 ${serverName} 可用工具:`, result.tools.map(t => t.name));
        
        return {
            success: true,
            tools: result.tools.map(tool => ({
                name: tool.name,
                description: tool.description,
                inputSchema: tool.inputSchema
            }))
        };
        
    } catch (error) {
        console.error(`❌ 获取工具列表失败 ${serverName}:`, error.message);
        return { success: false, error: error.message };
    }
}

/**
 * 调用MCP工具
 */
async function callTool(serverName, toolName, arguments_) {
    try {
        const client = mcpClients.get(serverName);
        if (!client) {
            throw new Error(`未找到服务连接: ${serverName}`);
        }
        
        console.log(`🔧 调用工具: ${serverName}.${toolName}`, arguments_);
        
        const result = await client.callTool({
            name: toolName,
            arguments: arguments_
        });
        
        console.log(`✅ 工具调用成功: ${serverName}.${toolName}`);
        
        // 提取文本内容
        let content = '';
        if (result.content && Array.isArray(result.content)) {
            content = result.content
                .filter(item => item.type === 'text')
                .map(item => item.text)
                .join('\n');
        } else if (result.content) {
            content = String(result.content);
        }
        
        return {
            success: true,
            result: content || JSON.stringify(result)
        };
        
    } catch (error) {
        console.error(`❌ 工具调用失败 ${serverName}.${toolName}:`, error.message);
        return { success: false, error: error.message };
    }
}

/**
 * 断开MCP服务连接
 */
async function disconnectFromMCPServer(serverName) {
    try {
        const client = mcpClients.get(serverName);
        if (client) {
            await client.close();
            mcpClients.delete(serverName);
            console.log(`🔌 已断开连接: ${serverName}`);
        }
        return { success: true };
    } catch (error) {
        console.error(`❌ 断开连接失败 ${serverName}:`, error.message);
        return { success: false, error: error.message };
    }
}

// API路由

/**
 * 连接到MCP服务
 * POST /connect
 * Body: { serverName, apiKey, config }
 */
app.post('/connect', async (req, res) => {
    const { serverName, apiKey, config } = req.body;
    
    if (!serverName || !apiKey) {
        return res.status(400).json({
            success: false,
            error: '缺少必要参数: serverName, apiKey'
        });
    }
    
    const result = await connectToMCPServer(serverName, apiKey, config);
    res.json(result);
});

/**
 * 获取可用工具
 * GET /tools/:serverName
 */
app.get('/tools/:serverName', async (req, res) => {
    const { serverName } = req.params;
    const result = await getAvailableTools(serverName);
    res.json(result);
});

/**
 * 调用工具
 * POST /call
 * Body: { serverName, toolName, arguments }
 */
app.post('/call', async (req, res) => {
    const { serverName, toolName, arguments: args } = req.body;
    
    if (!serverName || !toolName) {
        return res.status(400).json({
            success: false,
            error: '缺少必要参数: serverName, toolName'
        });
    }
    
    const result = await callTool(serverName, toolName, args || {});
    res.json(result);
});

/**
 * 断开连接
 * POST /disconnect
 * Body: { serverName }
 */
app.post('/disconnect', async (req, res) => {
    const { serverName } = req.body;
    
    if (!serverName) {
        return res.status(400).json({
            success: false,
            error: '缺少必要参数: serverName'
        });
    }
    
    const result = await disconnectFromMCPServer(serverName);
    res.json(result);
});

/**
 * 健康检查
 * GET /health
 */
app.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'MCP桥接服务运行正常',
        activeConnections: Array.from(mcpClients.keys())
    });
});

/**
 * 获取所有活跃连接
 * GET /connections
 */
app.get('/connections', (req, res) => {
    res.json({
        success: true,
        connections: Array.from(mcpClients.keys())
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 MCP桥接服务启动成功`);
    console.log(`📡 监听端口: ${PORT}`);
    console.log(`🔗 健康检查: http://localhost:${PORT}/health`);
});

// 优雅关闭
process.on('SIGINT', async () => {
    console.log('\n🛑 正在关闭MCP桥接服务...');
    
    // 关闭所有MCP连接
    for (const [serverName, client] of mcpClients) {
        try {
            await client.close();
            console.log(`🔌 已断开: ${serverName}`);
        } catch (error) {
            console.error(`❌ 断开失败 ${serverName}:`, error.message);
        }
    }
    
    process.exit(0);
});
