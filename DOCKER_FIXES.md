# Docker "can't start new thread" 错误修复说明

## 问题分析

你的Docker容器出现"can't start new thread"错误的主要原因：

### 1. 线程资源耗尽
- **MCP客户端**：在`functions/universal_mcp.py`中使用`ThreadPoolExecutor`创建过多线程
- **事件循环管理**：在`functions/mcp_client.py`中频繁创建新的事件循环
- **异步操作**：Streamlit与asyncio事件循环冲突导致线程泄漏

### 2. Docker配置问题
- **用户权限**：Dockerfile中创建了非root用户但未正确切换
- **资源限制**：没有设置容器的CPU和内存限制
- **系统限制**：没有配置进程数和文件描述符限制

### 3. Streamlit配置问题
- **开发模式**：启用了不必要的开发功能消耗资源
- **文件监控**：启用了文件变化监控增加线程使用
- **遥测功能**：启用了数据收集功能

## 修复方案

### 1. 代码层面修复

#### A. 优化线程池使用
```python
# 修复前：无限制创建线程
with concurrent.futures.ThreadPoolExecutor() as executor:

# 修复后：限制线程数量
with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
```

#### B. 改进事件循环管理
```python
# 修复前：频繁创建新循环
loop = asyncio.new_event_loop()
asyncio.set_event_loop(loop)

# 修复后：复用现有循环或安全创建
try:
    loop = asyncio.get_running_loop()
    # 使用线程池执行
except RuntimeError:
    # 创建新循环并确保清理
```

### 2. Docker配置修复

#### A. Dockerfile优化
- ✅ 正确切换到非root用户：`USER appuser`
- ✅ 添加优化的启动脚本
- ✅ 设置合适的Streamlit参数

#### B. docker-compose.yml优化
- ✅ 添加资源限制：内存2G，CPU 2核
- ✅ 设置系统限制：进程数4096，文件描述符65536
- ✅ 配置重启策略

#### C. docker-entrypoint.sh优化
- ✅ 设置系统资源限制
- ✅ 配置Python线程环境变量
- ✅ 禁用不必要的Streamlit功能

### 3. Streamlit配置优化

#### A. 创建.streamlit/config.toml
- ✅ 禁用开发模式功能
- ✅ 关闭文件监控
- ✅ 禁用遥测数据收集
- ✅ 优化内存使用

#### B. 创建优化启动脚本
- ✅ 限制线程栈大小
- ✅ 使用全局线程池
- ✅ 添加资源清理机制
- ✅ 注册信号处理器

## 使用方法

### 1. 重新构建镜像
```bash
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### 2. 监控资源使用
```bash
# 查看容器资源使用情况
docker stats llm_assistant

# 查看容器日志
docker logs -f llm_assistant

# 进入容器检查进程
docker exec -it llm_assistant ps aux
```

### 3. 验证修复效果
- 容器应该能够正常启动且不再出现线程错误
- 内存使用应该稳定在合理范围内
- 应用响应应该更加稳定

## 预期效果

1. **消除线程错误**：不再出现"can't start new thread"错误
2. **提高稳定性**：应用运行更加稳定，减少崩溃
3. **优化性能**：减少不必要的资源消耗
4. **改善响应**：用户交互响应更快

## 故障排除

如果仍然出现问题，可以尝试：

1. **进一步限制资源**：
   ```yaml
   deploy:
     resources:
       limits:
         memory: 1G
         cpus: '1.0'
   ```

2. **检查系统限制**：
   ```bash
   docker exec -it llm_assistant ulimit -a
   ```

3. **监控线程使用**：
   ```bash
   docker exec -it llm_assistant ps -eLf | wc -l
   ```

4. **查看详细错误**：
   ```bash
   docker logs llm_assistant 2>&1 | grep -i thread
   ```
