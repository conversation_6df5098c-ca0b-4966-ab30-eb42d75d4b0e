version: '3'
services:
  app:
    container_name: llm_assistant
    build: .
    ports:
      - "8501:8501"
    volumes:
      - ./data:/app/data
    environment:
      - PYTHONUNBUFFERED=1
      - MCP_ENABLED=false
      - MCP_CONFIG={}
    restart: unless-stopped
    # 设置资源限制以防止线程耗尽
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    # 设置系统限制
    ulimits:
      nproc: 4096  # 最大进程数
      nofile: 65536  # 最大文件描述符数

networks:
  default:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
